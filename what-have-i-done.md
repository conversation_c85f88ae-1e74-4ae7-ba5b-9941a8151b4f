2025.Aug.12
installed new backsatge instance using npx @backstage/create-app@latest

created .env and .env.keys, added them to the .gitignore
yarn add @dotenvx/dotenvx
// Load environment variables from root .env file
require('@dotenvx/dotenvx').config({
  path: '../../.env'
});

added pg connection to the app-config.yaml

yarn new
created frontend-plugin
created backend-plugin


when creating the new plugin it route path is automatically added to the App.tsx

import { ChoreoPage } from '@internal/plugin-choreo';
<Route path="/choreo" element={<ChoreoPage />} />

also to the backend index.ts
backend.add(import('@internal/plugin-choreo-backend'));


added choreo to the sidebar in Root.tsx
<SidebarItem icon={CopyrightIcon} to="/choreo" text="Choreo" />