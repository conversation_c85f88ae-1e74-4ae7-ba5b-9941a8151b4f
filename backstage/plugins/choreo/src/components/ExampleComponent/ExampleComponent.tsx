import {
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  makeStyles,
  Theme
} from '@material-ui/core';
import {
  Page,
  Content,
} from '@backstage/core-components';
import {
  Business as GoogleIcon,
  GitHub as GitHubIcon,
  Business as MicrosoftIcon,
  Business as EnterpriseIcon,
  Email as EmailIcon,
} from '@material-ui/icons';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    minHeight: '100vh',
    backgroundColor: theme.palette.background.default,
  },
  container: {
    height: '100vh',
    padding: theme.spacing(4),
  },
  signInCard: {
    padding: theme.spacing(3),
    height: 'fit-content',
  },
  signInTitle: {
    marginBottom: theme.spacing(3),
    textAlign: 'center',
  },
  signInButton: {
    width: '100%',
    marginBottom: theme.spacing(2),
    justifyContent: 'flex-start',
    textTransform: 'none',
  },
}));


const signInOptions = [
  { icon: GoogleIcon, label: 'Continue with Google' },
  { icon: GitHubIcon, label: 'Continue with GitHub' },
  { icon: MicrosoftIcon, label: 'Continue with Microsoft' },
  { icon: EnterpriseIcon, label: 'Sign in with Enterprise ID' },
  { icon: EmailIcon, label: 'Sign in with Email' },
];

export const ExampleComponent = () => {
  const classes = useStyles();

  
  const handleNavigation = () => {
    window.location.href = '/choreo/home';
  };

  return (
    <Page themeId="home" className={classes.root}>
      <Content className={classes.container}>
        <Grid
          container
          spacing={4}
          style={{ height: '100%' }}
          alignItems="center"
          justifyContent="center"
        >
          <Grid item xs={12} md={4}>
            <Card className={classes.signInCard}>
              <CardContent>
                <Typography variant="h5" className={classes.signInTitle}>
                  Sign In
                </Typography>
                {signInOptions.map((option, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    className={classes.signInButton}
                    startIcon={<option.icon />}
                    onClick={handleNavigation}
                  >
                    {option.label}
                  </Button>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};